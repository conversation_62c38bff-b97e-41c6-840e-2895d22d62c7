#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd

# Create test data
data = {
    'SN': ['SN001', 'SN002', 'SN003', 'SN004', 'SN005'],
    'IMEI_df1': ['123456789012345', '123456789012346', '123456789012347', '123456789012348', '123456789012349'],
    'HW_ID': ['1S00', '1S00', '1S01', 'nan', '1S00'],  # One different value, one 'nan' string
    'Other_Col': ['A', 'A', 'A', 'A', 'NaN']  # One 'NaN' string
}

df = pd.DataFrame(data)

# Save as Excel file
df.to_excel('test_data.xlsx', index=False)
print("Test Excel file created: test_data.xlsx")
print("Data:")
print(df)
