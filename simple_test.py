#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd

def strip_whitespace(value):
    """Eltávolítja a szóközöket az érték elejéről és végéről."""
    if pd.isna(value) or value == '':
        return value
    # Convert to string and strip whitespace
    str_value = str(value).strip()
    # Check if the value is 'nan' string and treat it as an error
    if str_value.lower() == 'nan':
        return 'HIBA: nan érték'
    return str_value

def test_function():
    print("Testing strip_whitespace function:")
    
    test_cases = [
        ("normal", "normal"),
        ("nan", "HIBA: nan érték"),
        ("NaN", "HIBA: nan érték"),
        ("  test  ", "test"),
        ("", ""),
    ]
    
    for input_val, expected in test_cases:
        result = strip_whitespace(input_val)
        status = "✅" if result == expected else "❌"
        print(f"{status} '{input_val}' -> '{result}' (expected: '{expected}')")

if __name__ == "__main__":
    test_function()
