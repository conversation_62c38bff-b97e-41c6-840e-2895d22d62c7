#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import sys
import os

# Add the M2M SIMEI excel checker directory to the path
sys.path.append('M2M SIMEI excel checker')

# Import the strip_whitespace function from the updated module
try:
    from importlib import import_module
    spec = import_module('M2M SIMEI v030')
    strip_whitespace = spec.strip_whitespace
    print("✅ Successfully imported strip_whitespace from M2M SIMEI v030")
except Exception as e:
    print(f"❌ Error importing: {e}")
    sys.exit(1)

def test_strip_whitespace():
    """Test the updated strip_whitespace function"""
    print("\n=== Testing strip_whitespace function ===")
    
    test_cases = [
        ("normal value", "normal value"),
        ("  spaced value  ", "spaced value"),
        ("nan", "HIBA: nan érték"),
        ("NaN", "HIBA: nan érték"),
        ("NAN", "HIBA: nan érték"),
        ("", ""),
        (None, None),
        (pd.NaType(), pd.NaType()),
        ("123", "123"),
        ("  nan  ", "HIBA: nan érték"),
    ]
    
    for input_val, expected in test_cases:
        try:
            result = strip_whitespace(input_val)
            if result == expected:
                print(f"✅ '{input_val}' -> '{result}' (expected: '{expected}')")
            else:
                print(f"❌ '{input_val}' -> '{result}' (expected: '{expected}')")
        except Exception as e:
            print(f"❌ Error with '{input_val}': {e}")

def test_hw_id_scenario():
    """Test HW_ID column handling scenario"""
    print("\n=== Testing HW_ID scenario ===")
    
    # Create test data with HW_ID column that should be treated as column difference, not duplicate
    data = {
        'SN': ['SN001', 'SN002', 'SN003', 'SN004'],
        'IMEI_df1': ['123456789012345', '123456789012346', '123456789012347', '123456789012348'],
        'HW_ID': ['1S00', '1S00', '1S01', '1S00'],  # One different value - should be column difference
        'Other_Col': ['A', 'A', 'A', 'A']
    }
    
    df = pd.DataFrame(data)
    print("Test DataFrame:")
    print(df)
    
    # Test HW_ID column values
    hw_id_values = df['HW_ID'].tolist()
    print(f"\nHW_ID values: {hw_id_values}")
    
    # Check for differences
    first_value = strip_whitespace(hw_id_values[0])
    differences = []
    for i, val in enumerate(hw_id_values):
        processed_val = strip_whitespace(val)
        if processed_val != first_value:
            differences.append(f"Row {i+2}: different value '{processed_val}' (expected: '{first_value}')")
    
    if differences:
        print(f"✅ HW_ID column differences detected (should be column differences, not duplicates):")
        for diff in differences:
            print(f"  - {diff}")
    else:
        print("✅ No HW_ID differences found")

def test_nan_scenario():
    """Test 'nan' string handling scenario"""
    print("\n=== Testing 'nan' string scenario ===")
    
    # Create test data with 'nan' string values
    data = {
        'SN': ['SN001', 'SN002', 'SN003'],
        'IMEI_df1': ['123456789012345', '123456789012346', '123456789012347'],
        'Test_Col': ['normal', 'nan', 'NaN']  # Mix of normal and 'nan' string values
    }
    
    df = pd.DataFrame(data)
    print("Test DataFrame:")
    print(df)
    
    # Test column values
    test_col_values = df['Test_Col'].tolist()
    print(f"\nTest_Col values: {test_col_values}")
    
    # Process values and check for errors
    processed_values = []
    errors = []
    for i, val in enumerate(test_col_values):
        processed_val = strip_whitespace(val)
        processed_values.append(processed_val)
        if processed_val.startswith('HIBA: nan érték'):
            errors.append(f"Row {i+2}: {processed_val}")
    
    print(f"Processed values: {processed_values}")
    
    if errors:
        print(f"✅ 'nan' string errors detected (should be treated as errors):")
        for error in errors:
            print(f"  - {error}")
    else:
        print("❌ No 'nan' string errors found")

if __name__ == "__main__":
    print("Testing HW_ID and 'nan' fixes...")
    
    test_strip_whitespace()
    test_hw_id_scenario()
    test_nan_scenario()
    
    print("\n=== Test completed ===")
